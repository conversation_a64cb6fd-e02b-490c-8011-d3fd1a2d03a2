import React from 'react';
import { Modal } from 'react-native';
import styled from 'styled-components/native';
import { Text } from '../../../components/Text';
import { ActiveTransfer } from '../../../hooks/useMyBidsPlayers';
import { Manager } from '../../../models/manager';

interface StyledProps {
  theme: any;
}

const ModalContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
`;

const ModalContent = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  border-radius: 12px;
  padding: 20px;
  width: 90%;
  max-width: 400px;
`;

const ModalTitle = styled(Text)`
  font-family: 'NunitoBold';
  font-size: 20px;
  margin-bottom: 16px;
  text-align: center;
`;

const InputContainer = styled.View`
  margin-bottom: 20px;
`;

const Label = styled(Text)`
  font-size: 14px;
  margin-bottom: 8px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

const ModalButtonContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  gap: 12px;
`;

const ActionButton = styled.TouchableOpacity<{ variant?: 'primary' | 'secondary' | 'danger' }>`
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  align-items: center;
  justify-content: center;
  background-color: ${(props) => {
    switch (props.variant) {
      case 'danger':
        return '#e3172a';
      case 'secondary':
        return '#888';
      default:
        return props.theme.colors.primary;
    }
  }};
`;

const ActionButtonText = styled(Text)`
  color: white;
  font-family: 'NunitoBold';
  font-size: 14px;
  text-align: center;
`;

const CloseButton = styled.TouchableOpacity`
  position: absolute;
  top: 16px;
  right: 16px;
  padding: 8px;
`;

interface TransferDetailsModalProps {
  visible: boolean;
  transfer: ActiveTransfer | null;
  manager?: Manager | null;
  onClose: () => void;
  onAcceptCounterOffer: (transfer: ActiveTransfer) => void;
  onNegotiate: (transfer: ActiveTransfer) => void;
  onCancelTransfer: (transfer: ActiveTransfer) => void;
}

export const TransferDetailsModal: React.FC<TransferDetailsModalProps> = ({
  visible,
  transfer,
  manager,
  onClose,
  onAcceptCounterOffer,
  onNegotiate,
  onCancelTransfer,
}) => {
  if (!transfer) return null;

  const hasCounterOffer = transfer.counterOfferValue !== '0' &&
                          transfer.counterOfferTime !== '0' &&
                          Number(transfer.counterOfferTime) > transfer.date;

  const isUserBuyer = transfer.buyer === manager?.team?.teamId;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <ModalContainer>
        <ModalContent>
          <CloseButton onPress={onClose}>
            <Text style={{ fontSize: 18 }}>×</Text>
          </CloseButton>
          <ModalTitle>Transfer Details</ModalTitle>

          <InputContainer>
            <Label>
              Player: {transfer.player.firstName} {transfer.player.surname}
            </Label>
            <Label>Your Offer: £{transfer.value.toLocaleString()}</Label>
            <Label>To: {transfer.seller.teamName}</Label>
            {hasCounterOffer && (
              <Label style={{ color: '#e3172a', fontFamily: 'NunitoBold' }}>
                Counter Offer: £{Number(transfer.counterOfferValue).toLocaleString()}
              </Label>
            )}
          </InputContainer>

          {/* Show action buttons based on transfer state */}
          {isUserBuyer ? (
            hasCounterOffer ? (
              // Show counter offer response buttons
              <ModalButtonContainer>
                <ActionButton
                  variant="primary"
                  onPress={() => {
                    onAcceptCounterOffer(transfer);
                    onClose();
                  }}
                >
                  <ActionButtonText>Accept</ActionButtonText>
                </ActionButton>
                <ActionButton
                  variant="secondary"
                  onPress={() => {
                    onNegotiate(transfer);
                    onClose();
                  }}
                >
                  <ActionButtonText>Negotiate</ActionButtonText>
                </ActionButton>
                <ActionButton
                  variant="danger"
                  onPress={() => {
                    onCancelTransfer(transfer);
                    onClose();
                  }}
                >
                  <ActionButtonText>You're having a laugh</ActionButtonText>
                </ActionButton>
              </ModalButtonContainer>
            ) : (
              // Show amend/withdraw buttons when no counter offer
              <ModalButtonContainer>
                <ActionButton
                  variant="primary"
                  onPress={() => {
                    onNegotiate(transfer);
                    onClose();
                  }}
                >
                  <ActionButtonText>Amend Offer</ActionButtonText>
                </ActionButton>
                <ActionButton
                  variant="danger"
                  onPress={() => {
                    onCancelTransfer(transfer);
                    onClose();
                  }}
                >
                  <ActionButtonText>Withdraw Offer</ActionButtonText>
                </ActionButton>
              </ModalButtonContainer>
            )
          ) : (
            // Not the buyer, just show close button
            <ModalButtonContainer>
              <ActionButton variant="secondary" onPress={onClose}>
                <ActionButtonText>Close</ActionButtonText>
              </ActionButton>
            </ModalButtonContainer>
          )}
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};
