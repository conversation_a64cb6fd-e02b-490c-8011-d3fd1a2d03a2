import { MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { Dimensions, TouchableOpacityProps } from 'react-native';
import { TransferListPlayer } from '../../models/player';
import PlayerInfo from './PlayerInfo';
import {
  AttributeGroup,
  AttributeLabel,
  AttributesContainer,
  AttributeValue,
  Card,
  CardContent,
  CardHeader,
  DetailButton,
} from './PlayerRowStyles';
import { AuctionInfo } from './SharedComponents';

interface TransferListPlayerRowProps extends TouchableOpacityProps {
  player: TransferListPlayer;
  isSelected?: boolean;
  backgroundColor?: string;
  isActive?: boolean;
  positionFilter?: 'All' | 'Goalkeeper' | 'Defender' | 'Midfielder' | 'Attacker';
  teamAverages?: Record<string, number>;
  onSelect?: (player: TransferListPlayer) => void;
  isHighestBidder?: boolean;
}

const TransferListPlayerRow: React.FC<TransferListPlayerRowProps> = ({
  player,
  isSelected = false,
  backgroundColor,
  isActive = false,
  positionFilter = 'All',
  onSelect,
  isHighestBidder = false,
  ...props
}) => {
  const handlePress = () => {
    if (onSelect) {
      onSelect(player);
    }
  };

  // Determine which attributes to show based on position filter
  const getAttributesToShow = () => {
    const { attributes } = player;

    switch (positionFilter) {
      case 'Goalkeeper':
        return [
          { label: 'Ref', value: attributes.reflexes },
          { label: 'Pos', value: attributes.positioning },
          { label: 'Stp', value: attributes.shotStopping },
        ];
      case 'Defender':
        return [
          { label: 'Tck', value: attributes.tackling },
          { label: 'Mrk', value: attributes.marking },
          { label: 'Hdr', value: attributes.heading },
        ];
      case 'Midfielder':
        return [
          { label: 'Pas', value: attributes.passing },
          { label: 'Vis', value: attributes.vision },
          { label: 'Ctl', value: attributes.ballControl },
        ];
      case 'Attacker':
        return [
          { label: 'Fin', value: attributes.finishing },
          { label: 'Pac', value: attributes.pace },
          { label: 'Crs', value: attributes.crossing },
        ];
      default:
        return [];
    }
  };

  const attributesToShow = getAttributesToShow();
  const showAttributes = attributesToShow.length > 0;

  // Check screen width to determine if we're on mobile
  const { width } = Dimensions.get('window');
  const isMobile = width < 768;

  // Determine card background color
  return (
    <Card isSelected={isSelected} backgroundColor={backgroundColor} isActive={isActive} {...props}>
      <CardContent>
        <CardHeader hasAttributes={showAttributes} isMobile={isMobile}>
          <PlayerInfo
            player={player}
            positionFilter={positionFilter}
            showImages={true}
            showValue={false}
          />
        </CardHeader>
      </CardContent>

      {/* Auction info on the right side */}
      <AuctionInfo player={player} isHighestBidder={isHighestBidder} />

      {/* Detail button on the right side */}
      {onSelect && (
        <DetailButton onPress={handlePress}>
          <MaterialIcons name="chevron-right" size={24} color="#ffffff" />
        </DetailButton>
      )}
    </Card>
  );
};

export default TransferListPlayerRow;
